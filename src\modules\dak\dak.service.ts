import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { DiagnosticRecord } from './entities/diagnosticRecord.entity';
import { DeterminationRecord } from './entities/determinationRecord.entity';
import { CreateDiagnosticRecordDto } from './dto/create-diagnostic-record.dto';
import { CreateDeterminationRecordDto } from './dto/create-determination-record.dto';
import { Users } from '../sxccduijie/entities/users.entity';
import { Employees } from '../sxccduijie/entities/employees.entity';
import { EmployeeBasicInfo } from './entities/employeeBasicInfo.entity';
import { HealthCheckRegister } from './entities/HealthCheckRegister.entity';
import { HealthSurvArchives } from './entities/HealthSurvArchives.entity';
import { OccupationalHistory } from './entities/OccupationalHistory.entity';
import * as moment from 'moment';

@Injectable()
export class DakService {
  private readonly logger = new Logger(DakService.name);

  constructor(
    @InjectRepository(DiagnosticRecord, 'mongodbConnection')
    private diagnosticRecordRepository: MongoRepository<DiagnosticRecord>,
    @InjectRepository(DeterminationRecord, 'mongodbConnection')
    private determinationRecordRepository: MongoRepository<DeterminationRecord>,
    @InjectRepository(Users, 'mongodbConnection')
    private usersRepository: MongoRepository<Users>,
    @InjectRepository(Employees, 'mongodbConnection')
    private employeesRepository: MongoRepository<Employees>,
    @InjectRepository(HealthCheckRegister, 'mongodbConnection')
    private healthCheckRegisterRepository: MongoRepository<HealthCheckRegister>,
    @InjectRepository(HealthSurvArchives, 'mongodbConnection')
    private healthSurvArchivesRepository: MongoRepository<HealthSurvArchives>,
    @InjectRepository(EmployeeBasicInfo, 'mongodbConnection')
    private employeeBasicInfoRepository: MongoRepository<EmployeeBasicInfo>,
    @InjectRepository(OccupationalHistory, 'mongodbConnection')
    private occupationalHistoryRepository: MongoRepository<OccupationalHistory>,
  ) { }

  /**
   * 创建或更新诊断记录
   * @param createDiagnosticRecordDto 诊断记录DTO
   * @returns 创建或更新的诊断记录
   */
  async createOrUpdateDiagnosticRecord(
    createDiagnosticRecordDto: CreateDiagnosticRecordDto,
  ): Promise<DiagnosticRecord> {
    try {
      // 查询是否存在相同身份证和诊断编号的记录
      const existingRecord = await this.diagnosticRecordRepository.findOne({
        where: {
          idNumber: createDiagnosticRecordDto.idNumber,
          diagnosisNumber: createDiagnosticRecordDto.diagnosisNumber,
        },
      });

      let result: DiagnosticRecord;

      if (existingRecord) {
        // 如果存在，则更新记录
        this.logger.log(
          `更新诊断记录: ${createDiagnosticRecordDto.idNumber} - ${createDiagnosticRecordDto.diagnosisNumber}`,
        );

        // 更新时间
        createDiagnosticRecordDto.updatedAt = new Date();

        // 更新记录
        await this.diagnosticRecordRepository.update(
          { _id: existingRecord._id },
          {
            diagnosisConclusionDescription: createDiagnosticRecordDto.diagnosisConclusion,
            ...createDiagnosticRecordDto,
          },
        );

        // 返回更新后的记录
        result = await this.diagnosticRecordRepository.findOne({
          where: { _id: existingRecord._id },
        });
      } else {
        // 如果不存在，则创建新记录
        this.logger.log(
          `创建诊断记录: ${createDiagnosticRecordDto.idNumber} - ${createDiagnosticRecordDto.diagnosisNumber}`,
        );

        // 设置创建和更新时间
        createDiagnosticRecordDto.createdAt = new Date();
        createDiagnosticRecordDto.updatedAt = new Date();

        // 创建并保存新记录
        const newRecord = this.diagnosticRecordRepository.create(
          {
            diagnosisConclusionDescription: createDiagnosticRecordDto.diagnosisConclusion,
            ...createDiagnosticRecordDto,
          },
        );
        result = await this.diagnosticRecordRepository.save(newRecord);
      }

      // 根据身份证号更新users表中的职业病相关字段
      await this.updateUserOccupationalDiseaseFromDiagnostic(
        createDiagnosticRecordDto.idNumber,
        createDiagnosticRecordDto.hasOccupationalDisease,
        createDiagnosticRecordDto.diagnosisConclusionDescription || createDiagnosticRecordDto.diagnosisConclusion,
        createDiagnosticRecordDto.occupationalDisease,
        createDiagnosticRecordDto.diagnosisNumber
      );

      return result;
    } catch (error) {
      this.logger.error(
        `创建或更新诊断记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 创建或更新鉴定记录
   * @param createDeterminationRecordDto 鉴定记录DTO
   * @returns 创建或更新的鉴定记录
   */
  async createOrUpdateDeterminationRecord(
    createDeterminationRecordDto: CreateDeterminationRecordDto,
  ): Promise<DeterminationRecord> {
    try {
      // 查询是否存在相同身份证和鉴定编号的记录
      const existingRecord = await this.determinationRecordRepository.findOne({
        where: {
          idNumber: createDeterminationRecordDto.idNumber,
          determinationNumber: createDeterminationRecordDto.determinationNumber,
        },
      });

      // 兼容 entity 字段的默认值
      if (!createDeterminationRecordDto.occupationalDisease) {
        createDeterminationRecordDto.occupationalDisease = [];
      }
      if (!createDeterminationRecordDto.fileList) {
        createDeterminationRecordDto.fileList = [];
      }
      if (!createDeterminationRecordDto.createdAt) {
        createDeterminationRecordDto.createdAt = new Date();
      }
      if (!createDeterminationRecordDto.updatedAt) {
        createDeterminationRecordDto.updatedAt = new Date();
      }

      if (existingRecord) {
        // 如果存在，则更新记录
        this.logger.log(
          `更新鉴定记录: ${createDeterminationRecordDto.idNumber} - ${createDeterminationRecordDto.determinationNumber}`,
        );

        // 更新时间
        createDeterminationRecordDto.updatedAt = new Date();

        // 更新记录
        await this.determinationRecordRepository.update(
          { _id: existingRecord._id },
          {
            // 只在 determinationConclusionDescription 为空时用 determinationConclusion
            determinationConclusionDescription: createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
            ...createDeterminationRecordDto
          },
        );

        // 返回更新后的记录
        const result = await this.determinationRecordRepository.findOne({
          where: { _id: existingRecord._id },
        });

        // 根据身份证号更新users表中的职业病相关字段
        await this.updateUserOccupationalDiseaseFromDetermination(
          createDeterminationRecordDto.idNumber,
          createDeterminationRecordDto.hasOccupationalDisease,
          createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
          createDeterminationRecordDto.occupationalDisease,
          createDeterminationRecordDto.diagnosisNumber,
          createDeterminationRecordDto.determinationNumber,
          createDeterminationRecordDto.firstDeterminationNumber
        );

        return result;
      } else {
        // 如果不存在，则创建新记录
        this.logger.log(
          `创建鉴定记录: ${createDeterminationRecordDto.idNumber} - ${createDeterminationRecordDto.determinationNumber}`,
        );

        // 设置创建和更新时间
        createDeterminationRecordDto.createdAt = new Date();
        createDeterminationRecordDto.updatedAt = new Date();

        // 创建并保存新记录
        const newRecord = this.determinationRecordRepository.create(
          {
            determinationConclusionDescription: createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
            ...createDeterminationRecordDto
          },
        );
        const result = await this.determinationRecordRepository.save(newRecord);

        // 根据身份证号更新users表中的职业病相关字段
        await this.updateUserOccupationalDiseaseFromDetermination(
          createDeterminationRecordDto.idNumber,
          createDeterminationRecordDto.hasOccupationalDisease,
          createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
          createDeterminationRecordDto.occupationalDisease,
          createDeterminationRecordDto.diagnosisNumber,
          createDeterminationRecordDto.determinationNumber,
          createDeterminationRecordDto.firstDeterminationNumber
        );

        return result;
      }
    } catch (error) {
      this.logger.error(
        `创建或更新鉴定记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号查询诊断记录
   * @param idNumber 身份证号
   * @returns 诊断记录列表
   */
  async findDiagnosticRecordsByIdNumber(idNumber: string): Promise<DiagnosticRecord[]> {
    try {
      return await this.diagnosticRecordRepository.find({
        where: { idNumber },
      });
    } catch (error) {
      this.logger.error(
        `查询诊断记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号查询鉴定记录
   * @param idNumber 身份证号
   * @returns 鉴定记录列表
   */
  async findDeterminationRecordsByIdNumber(idNumber: string): Promise<DeterminationRecord[]> {
    try {
      return await this.determinationRecordRepository.find({
        where: { idNumber },
      });
    } catch (error) {
      this.logger.error(
        `查询鉴定记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据诊断记录更新用户的职业病相关信息
   * @param idNumber 身份证号
   * @param hasOccupationalDisease 是否患有职业病
   * @param diseaseDescription 职业病描述
   * @param occupationalDisease 职业病数组
   * @param diagnosisNumber 诊断编号（用于日志记录）
   */
  private async updateUserOccupationalDiseaseFromDiagnostic(
    idNumber: string,
    hasOccupationalDisease: boolean,
    diseaseDescription: string,
    occupationalDisease: { name: string; code: string }[],
    diagnosisNumber: string
  ): Promise<void> {
    try {
      // 查找用户
      const user = await this.usersRepository.findOne({
        where: { idNo: idNumber },
      });

      if (!user) {
        this.logger.warn(`未找到用户: ${idNumber}`);
        return;
      }

      // 如果诊断结果为非职业病，不更新用户信息
      if (!hasOccupationalDisease) {
        this.logger.log(`诊断结果为非职业病，不更新用户信息: ${idNumber}`);
        return;
      }

      // 获取用户当前的职业病列表
      let userOccupationalDiseases = user.occupationalDisease || [];

      // 将新的职业病添加到列表中
      if (occupationalDisease && occupationalDisease.length > 0) {
        // 合并职业病列表
        const combinedDiseases = [...userOccupationalDiseases, ...occupationalDisease];

        // 去重 (基于name和code)
        userOccupationalDiseases = combinedDiseases.filter((disease, index, self) =>
          index === self.findIndex(d => d.name === disease.name && d.code === disease.code)
        );
      }

      // 更新用户信息
      this.logger.log(`更新用户职业病信息(诊断 ${diagnosisNumber}): ${idNumber}`);
      await this.usersRepository.update(
        { _id: user._id },
        {
          hasOccupationalDisease: true, // 如果有职业病，设置为true
          occupationalDiseaseDescription: diseaseDescription || '',
          occupationalDisease: userOccupationalDiseases,
          updateTime: new Date(),
        }
      );

    } catch (error) {
      this.logger.error(
        `更新用户职业病信息(诊断)失败: ${error.message}`,
        error.stack,
      );
      // 不抛出异常，以免影响主流程
    }
  }

  /**
   * 根据鉴定记录更新用户的职业病相关信息
   * @param idNumber 身份证号
   * @param hasOccupationalDisease 是否患有职业病
   * @param diseaseDescription 职业病描述
   * @param occupationalDisease 职业病数组
   * @param diagnosisNumber 诊断编号
   * @param determinationNumber 鉴定编号
   * @param firstDeterminationNumber 首次鉴定编号
   */
  private async updateUserOccupationalDiseaseFromDetermination(
    idNumber: string,
    hasOccupationalDisease: boolean,
    diseaseDescription: string,
    occupationalDisease: { name: string; code: string }[],
    diagnosisNumber: string,
    determinationNumber: string,
    firstDeterminationNumber?: string
  ): Promise<void> {
    try {
      // 查找用户
      const user = await this.usersRepository.findOne({
        where: { idNo: idNumber },
      });

      if (!user) {
        this.logger.warn(`未找到用户: ${idNumber}`);
        return;
      }

      // 获取用户当前的职业病列表
      let userOccupationalDiseases = user.occupationalDisease || [];

      // 查找对应的诊断记录
      const diagnosticRecord = await this.diagnosticRecordRepository.findOne({
        where: { diagnosisNumber: diagnosisNumber }
      });

      if (!diagnosticRecord) {
        this.logger.warn(`未找到对应的诊断记录: ${diagnosisNumber}`);
      } else {
        // 如果找到了对应的诊断记录，需要从用户的职业病列表中移除该诊断记录中的职业病
        if (diagnosticRecord.occupationalDisease && diagnosticRecord.occupationalDisease.length > 0) {
          // 过滤掉诊断记录中的职业病
          userOccupationalDiseases = userOccupationalDiseases.filter(disease => {
            return !diagnosticRecord.occupationalDisease.some(d =>
              d.name === disease.name && d.code === disease.code
            );
          });
        }
      }

      // 如果是再鉴定，还需要查找并处理首次鉴定的记录
      if (firstDeterminationNumber) {
        const firstDeterminationRecord = await this.determinationRecordRepository.findOne({
          where: { determinationNumber: firstDeterminationNumber }
        });

        if (firstDeterminationRecord) {
          // 如果找到了首次鉴定记录，需要从用户的职业病列表中移除该鉴定记录中的职业病
          if (firstDeterminationRecord.occupationalDisease && firstDeterminationRecord.occupationalDisease.length > 0) {
            // 过滤掉首次鉴定记录中的职业病
            userOccupationalDiseases = userOccupationalDiseases.filter(disease => {
              return !firstDeterminationRecord.occupationalDisease.some(d =>
                d.name === disease.name && d.code === disease.code
              );
            });
          }
        }
      }

      // 将新的职业病添加到列表中
      if (hasOccupationalDisease && occupationalDisease && occupationalDisease.length > 0) {
        // 合并职业病列表
        const combinedDiseases = [...userOccupationalDiseases, ...occupationalDisease];

        // 去重 (基于name和code)
        userOccupationalDiseases = combinedDiseases.filter((disease, index, self) =>
          index === self.findIndex(d => d.name === disease.name && d.code === disease.code)
        );
      }

      // 检查用户是否还有职业病
      const stillHasOccupationalDisease = userOccupationalDiseases.length > 0;

      // 更新用户信息
      this.logger.log(`更新用户职业病信息(鉴定 ${determinationNumber}): ${idNumber}`);
      await this.usersRepository.update(
        { _id: user._id },
        {
          hasOccupationalDisease: stillHasOccupationalDisease,
          occupationalDiseaseDescription: stillHasOccupationalDisease ? (diseaseDescription || '') : '',
          occupationalDisease: userOccupationalDiseases,
          updateTime: new Date(),
        }
      );

    } catch (error) {
      this.logger.error(
        `更新用户职业病信息(鉴定)失败: ${error.message}`,
        error.stack,
      );
      // 不抛出异常，以免影响主流程
    }
  }

  async updateDiagnosisStatus(
    checkNo: string,
    status: string,
  ): Promise<string> {
    try {
      const healthCheck = await this.healthCheckRegisterRepository.findOne({
        where: { checkNo },
      });

      if (!healthCheck) {
        this.logger.warn(`未找到体检记录: ${checkNo}`);
        return null;
      }

      await this.healthCheckRegisterRepository.update(
        { _id: healthCheck._id },
        { diagnosisStatus: status as any },
      );

      return '更新体检记录成功';
    } catch (error) {
      this.logger.error(
        `更新体检记录(诊断状态)失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号抽取劳动者职业健康监护档案
   * @param idNumber 身份证号
   * @returns 创建的职业健康监护档案
   */
  async extractHealthArchive(idNumber: string): Promise<HealthSurvArchives> {
    try {
      // 1. 一般概况
      let basicData = <any>{
        idNumber,
      }
      let user = await this.usersRepository.findOne({})
      if (!user) {
        throw new Error(`未找到身份证号为 ${idNumber} 的用户`);
      }
      const employee = await this.employeesRepository.findOne({
        where: { _id: user.employeeId },
      });

      const birthDate = idNumber.substring(6, 14);
      const age = moment().diff(birthDate, 'years');

      const { name, gender, phoneNum } = user
      const { hobby, marriage, nativePlace } = employee;

      basicData = {
        ...basicData,
        name,
        gender,
        age,
        phoneNum,
        hobby,
        marriage,
        nativePlace,
        EnterpriseID: user.companyId[user.companyId.length - 1],
        employeeId: user.employeeId,
        userId: user._id,
      }

      // 2. 职业史
      let workHistory = []
      // 3. 接触史
      let exposureHistory = []

      const occupationalHistories = await this.occupationalHistoryRepository.find({
        where: { employeeId: user.employeeId },
      });

      occupationalHistories && occupationalHistories.forEach(oh => {
        const temp = {
          entryTime: oh.entryTime,
          leaveTime: oh.leaveTime,
          workUnit: oh.workUnit,
          workshop: oh.workshop,
          station: oh.station,
          workType: oh.workType,
        }
        workHistory.push(temp)
        exposureHistory.push({ ...temp, harmFactors: oh.harmFactors })
      })

      // 4. 既往史
      let pastHistory = []

      const employeeBasicInfo = await this.employeeBasicInfoRepository.findOne({
        where: { IDNum: idNumber },
      });

      employeeBasicInfo && employeeBasicInfo.pastHistory.forEach(ph => {
        pastHistory.push({
          diseaseName: ph.diseaseName,
          diagnosisDate: ph.diagnosisDate,
          institutionName: ph.institutionName,
          treatmentProcess: ph.treatmentProcess,
          outcomeCode: ph.outcomeCode,
        })
      })


      // 5. 作业场所危害因素检测结果
      let workspaceHarmResult = []
      // 6. 职业健康检查结果及处理情况
      let healthCheckResult = []

      const healthCheckRegisters = await this.healthCheckRegisterRepository.aggregate([
        { $match: { idNumber, status: 3 } },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'physicalOrgID',
            foreignField: '_id',
            as: 'physicalOrg'
          }
        },
        { $addFields: { physicalOrgName: { $arrayElemAt: ['$physicalOrg.name', 0] } } },
        { $project: { physicalOrg: 0, } },
      ]).toArray();
      healthCheckRegisters && healthCheckRegisters.forEach(hcr => {
        healthCheckResult.push({
          checkNo: hcr.checkNo,
          reportTime: hcr.reportTime || hcr.registerTime,
          checkConclusion: hcr.jobConclusion,
          physicalOrgName: hcr.physicalOrgName
        })
      })


      const res = await this.healthSurvArchivesRepository.save({});
      return res;

    }
    catch (error) {
      this.logger.error(
        `创建职业健康监护档案失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  
}